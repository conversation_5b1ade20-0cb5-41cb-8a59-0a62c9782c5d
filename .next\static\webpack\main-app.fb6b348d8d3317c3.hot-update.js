"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main-app",{

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-index.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-index.js ***!
  \*******************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// imports polyfill from `@next/polyfill-module` after build.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hydrate\", ({\n    enumerable: true,\n    get: function() {\n        return hydrate;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js\");\n__webpack_require__(/*! ../build/polyfills/polyfill-module */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/polyfills/polyfill-module.js\");\n__webpack_require__(/*! ./components/globals/patch-console */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/globals/patch-console.js\");\n__webpack_require__(/*! ./components/globals/handle-global-errors */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/globals/handle-global-errors.js\");\nconst _client = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom/client */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react-dom/client.js\"));\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\"));\nconst _client1 = __webpack_require__(/*! react-server-dom-webpack/client */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react-server-dom-webpack/client.js\");\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ../shared/lib/head-manager-context.shared-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _onrecoverableerror = __webpack_require__(/*! ./react-client-callbacks/on-recoverable-error */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js\");\nconst _errorboundarycallbacks = __webpack_require__(/*! ./react-client-callbacks/error-boundary-callbacks */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js\");\nconst _appcallserver = __webpack_require__(/*! ./app-call-server */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-call-server.js\");\nconst _appfindsourcemapurl = __webpack_require__(/*! ./app-find-source-map-url */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-find-source-map-url.js\");\nconst _actionqueue = __webpack_require__(/*! ../shared/lib/router/action-queue */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/router/action-queue.js\");\nconst _approuter = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./components/app-router */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/app-router.js\"));\nconst _createinitialrouterstate = __webpack_require__(/*! ./components/router-reducer/create-initial-router-state */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _appbuildid = __webpack_require__(/*! ./app-build-id */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-build-id.js\");\nconst _iserrorthrownwhilerenderingrsc = __webpack_require__(/*! ./lib/is-error-thrown-while-rendering-rsc */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/lib/is-error-thrown-while-rendering-rsc.js\");\n/// <reference types=\"react-dom/experimental\" />\nconst appElement = document;\nconst encoder = new TextEncoder();\nlet initialServerDataBuffer = undefined;\nlet initialServerDataWriter = undefined;\nlet initialServerDataLoaded = false;\nlet initialServerDataFlushed = false;\nlet initialFormStateData = null;\nfunction nextServerDataCallback(seg) {\n    if (seg[0] === 0) {\n        initialServerDataBuffer = [];\n    } else if (seg[0] === 1) {\n        if (!initialServerDataBuffer) throw Object.defineProperty(new Error('Unexpected server data: missing bootstrap script.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E18\",\n            enumerable: false,\n            configurable: true\n        });\n        if (initialServerDataWriter) {\n            initialServerDataWriter.enqueue(encoder.encode(seg[1]));\n        } else {\n            initialServerDataBuffer.push(seg[1]);\n        }\n    } else if (seg[0] === 2) {\n        initialFormStateData = seg[1];\n    } else if (seg[0] === 3) {\n        if (!initialServerDataBuffer) throw Object.defineProperty(new Error('Unexpected server data: missing bootstrap script.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E18\",\n            enumerable: false,\n            configurable: true\n        });\n        // Decode the base64 string back to binary data.\n        const binaryString = atob(seg[1]);\n        const decodedChunk = new Uint8Array(binaryString.length);\n        for(var i = 0; i < binaryString.length; i++){\n            decodedChunk[i] = binaryString.charCodeAt(i);\n        }\n        if (initialServerDataWriter) {\n            initialServerDataWriter.enqueue(decodedChunk);\n        } else {\n            initialServerDataBuffer.push(decodedChunk);\n        }\n    }\n}\nfunction isStreamErrorOrUnfinished(ctr) {\n    // If `desiredSize` is null, it means the stream is closed or errored. If it is lower than 0, the stream is still unfinished.\n    return ctr.desiredSize === null || ctr.desiredSize < 0;\n}\n// There might be race conditions between `nextServerDataRegisterWriter` and\n// `DOMContentLoaded`. The former will be called when React starts to hydrate\n// the root, the latter will be called when the DOM is fully loaded.\n// For streaming, the former is called first due to partial hydration.\n// For non-streaming, the latter can be called first.\n// Hence, we use two variables `initialServerDataLoaded` and\n// `initialServerDataFlushed` to make sure the writer will be closed and\n// `initialServerDataBuffer` will be cleared in the right time.\nfunction nextServerDataRegisterWriter(ctr) {\n    if (initialServerDataBuffer) {\n        initialServerDataBuffer.forEach((val)=>{\n            ctr.enqueue(typeof val === 'string' ? encoder.encode(val) : val);\n        });\n        if (initialServerDataLoaded && !initialServerDataFlushed) {\n            if (isStreamErrorOrUnfinished(ctr)) {\n                ctr.error(Object.defineProperty(new Error('The connection to the page was unexpectedly closed, possibly due to the stop button being clicked, loss of Wi-Fi, or an unstable internet connection.'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E117\",\n                    enumerable: false,\n                    configurable: true\n                }));\n            } else {\n                ctr.close();\n            }\n            initialServerDataFlushed = true;\n            initialServerDataBuffer = undefined;\n        }\n    }\n    initialServerDataWriter = ctr;\n}\n// When `DOMContentLoaded`, we can close all pending writers to finish hydration.\nconst DOMContentLoaded = function() {\n    if (initialServerDataWriter && !initialServerDataFlushed) {\n        initialServerDataWriter.close();\n        initialServerDataFlushed = true;\n        initialServerDataBuffer = undefined;\n    }\n    initialServerDataLoaded = true;\n};\n_c = DOMContentLoaded;\n// It's possible that the DOM is already loaded.\nif (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', DOMContentLoaded, false);\n} else {\n    // Delayed in marco task to ensure it's executed later than hydration\n    setTimeout(DOMContentLoaded);\n}\nconst nextServerDataLoadingGlobal = self.__next_f = self.__next_f || [];\nnextServerDataLoadingGlobal.forEach(nextServerDataCallback);\nnextServerDataLoadingGlobal.push = nextServerDataCallback;\nconst readable = new ReadableStream({\n    start (controller) {\n        nextServerDataRegisterWriter(controller);\n    }\n});\nconst initialServerResponse = (0, _client1.createFromReadableStream)(readable, {\n    callServer: _appcallserver.callServer,\n    findSourceMapURL: _appfindsourcemapurl.findSourceMapURL\n});\n// React overrides `.then` and doesn't return a new promise chain,\n// so we wrap the action queue in a promise to ensure that its value\n// is defined when the promise resolves.\n// https://github.com/facebook/react/blob/163365a07872337e04826c4f501565d43dbd2fd4/packages/react-client/src/ReactFlightClient.js#L189-L190\nconst pendingActionQueue = new Promise((resolve, reject)=>{\n    initialServerResponse.then((initialRSCPayload)=>{\n        // setAppBuildId should be called only once, during JS initialization\n        // and before any components have hydrated.\n        (0, _appbuildid.setAppBuildId)(initialRSCPayload.b);\n        resolve((0, _actionqueue.createMutableActionQueue)((0, _createinitialrouterstate.createInitialRouterState)({\n            initialFlightData: initialRSCPayload.f,\n            initialCanonicalUrlParts: initialRSCPayload.c,\n            initialParallelRoutes: new Map(),\n            location: window.location,\n            couldBeIntercepted: initialRSCPayload.i,\n            postponed: initialRSCPayload.s,\n            prerendered: initialRSCPayload.S\n        })));\n    }, (err)=>reject(err));\n});\nfunction ServerRoot() {\n    const initialRSCPayload = (0, _react.use)(initialServerResponse);\n    const actionQueue = (0, _react.use)(pendingActionQueue);\n    const router = /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuter.default, {\n        actionQueue: actionQueue,\n        globalErrorComponentAndStyles: initialRSCPayload.G,\n        assetPrefix: initialRSCPayload.p\n    });\n    if ( true && initialRSCPayload.m) {\n        // We provide missing slot information in a context provider only during development\n        // as we log some additional information about the missing slots in the console.\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.MissingSlotContext, {\n            value: initialRSCPayload.m,\n            children: router\n        });\n    }\n    return router;\n}\n_c1 = ServerRoot;\nconst StrictModeIfEnabled =  true ? _react.default.StrictMode : 0;\nfunction Root(param) {\n    let { children } = param;\n    if (false) {}\n    return children;\n}\n_c2 = Root;\nconst reactRootOptions = {\n    onRecoverableError: _onrecoverableerror.onRecoverableError,\n    onCaughtError: _errorboundarycallbacks.onCaughtError,\n    onUncaughtError: _errorboundarycallbacks.onUncaughtError\n};\nfunction hydrate() {\n    var _window___next_root_layout_missing_tags;\n    const reactEl = /*#__PURE__*/ (0, _jsxruntime.jsx)(StrictModeIfEnabled, {\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_headmanagercontextsharedruntime.HeadManagerContext.Provider, {\n            value: {\n                appDir: true\n            },\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Root, {\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(ServerRoot, {})\n            })\n        })\n    });\n    if (document.documentElement.id === '__next_error__' || !!((_window___next_root_layout_missing_tags = window.__next_root_layout_missing_tags) == null ? void 0 : _window___next_root_layout_missing_tags.length)) {\n        let element = reactEl;\n        // Server rendering failed, fall back to client-side rendering\n        if ( true && (0, _iserrorthrownwhilerenderingrsc.shouldRenderRootLevelErrorOverlay)()) {\n            const { createRootLevelDevOverlayElement } = __webpack_require__(/*! ./components/react-dev-overlay/app/client-entry */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/app/client-entry.js\");\n            // Note this won't cause hydration mismatch because we are doing CSR w/o hydration\n            element = createRootLevelDevOverlayElement(element);\n        }\n        _client.default.createRoot(appElement, reactRootOptions).render(element);\n    } else {\n        _react.default.startTransition(()=>{\n            _client.default.hydrateRoot(appElement, reactEl, {\n                ...reactRootOptions,\n                formState: initialFormStateData\n            });\n        });\n    }\n    // TODO-APP: Remove this logic when Float has GC built-in in development.\n    if (true) {\n        const { linkGc } = __webpack_require__(/*! ./app-link-gc */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-link-gc.js\");\n        linkGc();\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-index.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"DOMContentLoaded\");\n$RefreshReg$(_c1, \"ServerRoot\");\n$RefreshReg$(_c2, \"Root\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/app-router.js":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/app-router.js ***!
  \*******************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createEmptyCacheNode: function() {\n        return createEmptyCacheNode;\n    },\n    createPrefetchURL: function() {\n        return createPrefetchURL;\n    },\n    default: function() {\n        return AppRouter;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _createhreffromurl = __webpack_require__(/*! ./router-reducer/create-href-from-url */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../../shared/lib/hooks-client-context.shared-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\nconst _usereducer = __webpack_require__(/*! ./use-reducer */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/use-reducer.js\");\nconst _errorboundary = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js\"));\nconst _isbot = __webpack_require__(/*! ../../shared/lib/router/utils/is-bot */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/add-base-path.js\");\nconst _approuterannouncer = __webpack_require__(/*! ./app-router-announcer */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/app-router-announcer.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _findheadincache = __webpack_require__(/*! ./router-reducer/reducers/find-head-in-cache */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js\");\nconst _unresolvedthenable = __webpack_require__(/*! ./unresolved-thenable */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/unresolved-thenable.js\");\nconst _removebasepath = __webpack_require__(/*! ../remove-base-path */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/remove-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../has-base-path */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/has-base-path.js\");\nconst _computechangedpath = __webpack_require__(/*! ./router-reducer/compute-changed-path */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _navfailurehandler = __webpack_require__(/*! ./nav-failure-handler */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/nav-failure-handler.js\");\nconst _appcallserver = __webpack_require__(/*! ../app-call-server */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-call-server.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/segment-cache.js\");\nconst _redirect = __webpack_require__(/*! ./redirect */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/redirect.js\");\nconst _redirecterror = __webpack_require__(/*! ./redirect-error */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/redirect-error.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./router-reducer/reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst _links = __webpack_require__(/*! ./links */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/links.js\");\nconst globalMutable = {};\nfunction isExternalURL(url) {\n    return url.origin !== window.location.origin;\n}\nfunction createPrefetchURL(href) {\n    // Don't prefetch for bots as they don't navigate.\n    if ((0, _isbot.isBot)(window.navigator.userAgent)) {\n        return null;\n    }\n    let url;\n    try {\n        url = new URL((0, _addbasepath.addBasePath)(href), window.location.href);\n    } catch (_) {\n        // TODO: Does this need to throw or can we just console.error instead? Does\n        // anyone rely on this throwing? (Seems unlikely.)\n        throw Object.defineProperty(new Error(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\"), \"__NEXT_ERROR_CODE\", {\n            value: \"E234\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    // Don't prefetch during development (improves compilation performance)\n    if (true) {\n        return null;\n    }\n    // External urls can't be prefetched in the same way.\n    if (isExternalURL(url)) {\n        return null;\n    }\n    return url;\n}\nfunction HistoryUpdater(param) {\n    let { appRouterState } = param;\n    (0, _react.useInsertionEffect)(()=>{\n        if (false) {}\n        const { tree, pushRef, canonicalUrl } = appRouterState;\n        const historyState = {\n            ...pushRef.preserveCustomHistoryState ? window.history.state : {},\n            // Identifier is shortened intentionally.\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            // __N is used to identify if the history entry can be handled by the old router.\n            __NA: true,\n            __PRIVATE_NEXTJS_INTERNALS_TREE: tree\n        };\n        if (pushRef.pendingPush && // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n        // This mirrors the browser behavior for normal navigation.\n        (0, _createhreffromurl.createHrefFromUrl)(new URL(window.location.href)) !== canonicalUrl) {\n            // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n            pushRef.pendingPush = false;\n            window.history.pushState(historyState, '', canonicalUrl);\n        } else {\n            window.history.replaceState(historyState, '', canonicalUrl);\n        }\n    }, [\n        appRouterState\n    ]);\n    (0, _react.useEffect)(()=>{\n        // The Next-Url and the base tree may affect the result of a prefetch\n        // task. Re-prefetch all visible links with the updated values. In most\n        // cases, this will not result in any new network requests, only if\n        // the prefetch result actually varies on one of these inputs.\n        if (false) {}\n    }, [\n        appRouterState.nextUrl,\n        appRouterState.tree\n    ]);\n    return null;\n}\n_c = HistoryUpdater;\nfunction createEmptyCacheNode() {\n    return {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null\n    };\n}\n/**\n * Server response that only patches the cache and tree.\n */ function useChangeByServerResponse(dispatch) {\n    return (0, _react.useCallback)((param)=>{\n        let { previousTree, serverResponse } = param;\n        (0, _react.startTransition)(()=>{\n            dispatch({\n                type: _routerreducertypes.ACTION_SERVER_PATCH,\n                previousTree,\n                serverResponse\n            });\n        });\n    }, [\n        dispatch\n    ]);\n}\nfunction useNavigate(dispatch) {\n    return (0, _react.useCallback)((href, navigateType, shouldScroll)=>{\n        const url = new URL((0, _addbasepath.addBasePath)(href), location.href);\n        if (false) {}\n        return dispatch({\n            type: _routerreducertypes.ACTION_NAVIGATE,\n            url,\n            isExternalUrl: isExternalURL(url),\n            locationSearch: location.search,\n            shouldScroll: shouldScroll != null ? shouldScroll : true,\n            navigateType,\n            allowAliasing: true\n        });\n    }, [\n        dispatch\n    ]);\n}\nfunction copyNextJsInternalHistoryState(data) {\n    if (data == null) data = {};\n    const currentState = window.history.state;\n    const __NA = currentState == null ? void 0 : currentState.__NA;\n    if (__NA) {\n        data.__NA = __NA;\n    }\n    const __PRIVATE_NEXTJS_INTERNALS_TREE = currentState == null ? void 0 : currentState.__PRIVATE_NEXTJS_INTERNALS_TREE;\n    if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n        data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE;\n    }\n    return data;\n}\nfunction Head(param) {\n    let { headCacheNode } = param;\n    // If this segment has a `prefetchHead`, it's the statically prefetched data.\n    // We should use that on initial render instead of `head`. Then we'll switch\n    // to `head` when the dynamic response streams in.\n    const head = headCacheNode !== null ? headCacheNode.head : null;\n    const prefetchHead = headCacheNode !== null ? headCacheNode.prefetchHead : null;\n    // If no prefetch data is available, then we go straight to rendering `head`.\n    const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head;\n    // We use `useDeferredValue` to handle switching between the prefetched and\n    // final values. The second argument is returned on initial render, then it\n    // re-renders with the first argument.\n    return (0, _react.useDeferredValue)(head, resolvedPrefetchRsc);\n}\n_c1 = Head;\n/**\n * The global router that wraps the application components.\n */ function Router(param) {\n    _s();\n    let { actionQueue, assetPrefix, globalError } = param;\n    const [state, dispatch] = (0, _usereducer.useReducer)(actionQueue);\n    const { canonicalUrl } = (0, _usereducer.useUnwrapState)(state);\n    // Add memoized pathname/query for useSearchParams and usePathname.\n    const { searchParams, pathname } = (0, _react.useMemo)(()=>{\n        const url = new URL(canonicalUrl,  false ? 0 : window.location.href);\n        return {\n            // This is turned into a readonly class in `useSearchParams`\n            searchParams: url.searchParams,\n            pathname: (0, _hasbasepath.hasBasePath)(url.pathname) ? (0, _removebasepath.removeBasePath)(url.pathname) : url.pathname\n        };\n    }, [\n        canonicalUrl\n    ]);\n    const changeByServerResponse = useChangeByServerResponse(dispatch);\n    const navigate = useNavigate(dispatch);\n    (0, _appcallserver.useServerActionDispatcher)(dispatch);\n    /**\n   * The app router that is exposed through `useRouter`. It's only concerned with dispatching actions to the reducer, does not hold state.\n   */ const appRouter = (0, _react.useMemo)(()=>{\n        const routerInstance = {\n            back: ()=>window.history.back(),\n            forward: ()=>window.history.forward(),\n            prefetch:  false ? // cache. So we don't need to dispatch an action.\n            0 : (href, options)=>{\n                // Use the old prefetch implementation.\n                const url = createPrefetchURL(href);\n                if (url !== null) {\n                    var _options_kind;\n                    // The prefetch reducer doesn't actually update any state or\n                    // trigger a rerender. It just writes to a mutable cache. So we\n                    // shouldn't bother calling setState/dispatch; we can just re-run\n                    // the reducer directly using the current state.\n                    // TODO: Refactor this away from a \"reducer\" so it's\n                    // less confusing.\n                    (0, _prefetchreducer.prefetchReducer)(actionQueue.state, {\n                        type: _routerreducertypes.ACTION_PREFETCH,\n                        url,\n                        kind: (_options_kind = options == null ? void 0 : options.kind) != null ? _options_kind : _routerreducertypes.PrefetchKind.FULL\n                    });\n                }\n            },\n            replace: (href, options)=>{\n                if (options === void 0) options = {};\n                (0, _react.startTransition)(()=>{\n                    var _options_scroll;\n                    navigate(href, 'replace', (_options_scroll = options.scroll) != null ? _options_scroll : true);\n                });\n            },\n            push: (href, options)=>{\n                if (options === void 0) options = {};\n                (0, _react.startTransition)(()=>{\n                    var _options_scroll;\n                    navigate(href, 'push', (_options_scroll = options.scroll) != null ? _options_scroll : true);\n                });\n            },\n            refresh: ()=>{\n                (0, _react.startTransition)(()=>{\n                    dispatch({\n                        type: _routerreducertypes.ACTION_REFRESH,\n                        origin: window.location.origin\n                    });\n                });\n            },\n            hmrRefresh: ()=>{\n                if (false) {} else {\n                    (0, _react.startTransition)(()=>{\n                        dispatch({\n                            type: _routerreducertypes.ACTION_HMR_REFRESH,\n                            origin: window.location.origin\n                        });\n                    });\n                }\n            }\n        };\n        return routerInstance;\n    }, [\n        actionQueue,\n        dispatch,\n        navigate\n    ]);\n    (0, _react.useEffect)(()=>{\n        // Exists for debugging purposes. Don't use in application code.\n        if (window.next) {\n            window.next.router = appRouter;\n        }\n    }, [\n        appRouter\n    ]);\n    if (true) {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const { cache, prefetchCache, tree } = (0, _usereducer.useUnwrapState)(state);\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            // Add `window.nd` for debugging purposes.\n            // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n            // @ts-ignore this is for debugging\n            window.nd = {\n                router: appRouter,\n                cache,\n                prefetchCache,\n                tree\n            };\n        }, [\n            appRouter,\n            cache,\n            prefetchCache,\n            tree\n        ]);\n    }\n    (0, _react.useEffect)(()=>{\n        // If the app is restored from bfcache, it's possible that\n        // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n        // would trigger the mpa navigation logic again from the lines below.\n        // This will restore the router to the initial state in the event that the app is restored from bfcache.\n        function handlePageShow(event) {\n            var _window_history_state;\n            if (!event.persisted || !((_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE)) {\n                return;\n            }\n            // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n            // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n            // of the last MPA navigation.\n            globalMutable.pendingMpaPath = undefined;\n            dispatch({\n                type: _routerreducertypes.ACTION_RESTORE,\n                url: new URL(window.location.href),\n                tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n            });\n        }\n        window.addEventListener('pageshow', handlePageShow);\n        return ()=>{\n            window.removeEventListener('pageshow', handlePageShow);\n        };\n    }, [\n        dispatch\n    ]);\n    (0, _react.useEffect)(()=>{\n        // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n        // are caught and handled by the router.\n        function handleUnhandledRedirect(event) {\n            const error = 'reason' in event ? event.reason : event.error;\n            if ((0, _redirecterror.isRedirectError)(error)) {\n                event.preventDefault();\n                const url = (0, _redirect.getURLFromRedirectError)(error);\n                const redirectType = (0, _redirect.getRedirectTypeFromError)(error);\n                if (redirectType === _redirecterror.RedirectType.push) {\n                    appRouter.push(url, {});\n                } else {\n                    appRouter.replace(url, {});\n                }\n            }\n        }\n        window.addEventListener('error', handleUnhandledRedirect);\n        window.addEventListener('unhandledrejection', handleUnhandledRedirect);\n        return ()=>{\n            window.removeEventListener('error', handleUnhandledRedirect);\n            window.removeEventListener('unhandledrejection', handleUnhandledRedirect);\n        };\n    }, [\n        appRouter\n    ]);\n    // When mpaNavigation flag is set do a hard navigation to the new url.\n    // Infinitely suspend because we don't actually want to rerender any child\n    // components with the new URL and any entangled state updates shouldn't\n    // commit either (eg: useTransition isPending should stay true until the page\n    // unloads).\n    //\n    // This is a side effect in render. Don't try this at home, kids. It's\n    // probably safe because we know this is a singleton component and it's never\n    // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n    // but that's... fine?)\n    const { pushRef } = (0, _usereducer.useUnwrapState)(state);\n    if (pushRef.mpaNavigation) {\n        // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n        if (globalMutable.pendingMpaPath !== canonicalUrl) {\n            const location1 = window.location;\n            if (pushRef.pendingPush) {\n                location1.assign(canonicalUrl);\n            } else {\n                location1.replace(canonicalUrl);\n            }\n            globalMutable.pendingMpaPath = canonicalUrl;\n        }\n        // TODO-APP: Should we listen to navigateerror here to catch failed\n        // navigations somehow? And should we call window.stop() if a SPA navigation\n        // should interrupt an MPA one?\n        (0, _react.use)(_unresolvedthenable.unresolvedThenable);\n    }\n    (0, _react.useEffect)(()=>{\n        const originalPushState = window.history.pushState.bind(window.history);\n        const originalReplaceState = window.history.replaceState.bind(window.history);\n        // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n        const applyUrlFromHistoryPushReplace = (url)=>{\n            var _window_history_state;\n            const href = window.location.href;\n            const tree = (_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE;\n            (0, _react.startTransition)(()=>{\n                dispatch({\n                    type: _routerreducertypes.ACTION_RESTORE,\n                    url: new URL(url != null ? url : href, href),\n                    tree\n                });\n            });\n        };\n        /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.pushState = function pushState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalPushState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalPushState(data, _unused, url);\n        };\n        /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.replaceState = function replaceState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalReplaceState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalReplaceState(data, _unused, url);\n        };\n        /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */ const onPopState = (event)=>{\n            if (!event.state) {\n                // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n                return;\n            }\n            // This case happens when the history entry was pushed by the `pages` router.\n            if (!event.state.__NA) {\n                window.location.reload();\n                return;\n            }\n            // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n            // Without startTransition works if the cache is there for this path\n            (0, _react.startTransition)(()=>{\n                dispatch({\n                    type: _routerreducertypes.ACTION_RESTORE,\n                    url: new URL(window.location.href),\n                    tree: event.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n                });\n            });\n        };\n        // Register popstate event to call onPopstate.\n        window.addEventListener('popstate', onPopState);\n        return ()=>{\n            window.history.pushState = originalPushState;\n            window.history.replaceState = originalReplaceState;\n            window.removeEventListener('popstate', onPopState);\n        };\n    }, [\n        dispatch\n    ]);\n    const { cache, tree, nextUrl, focusAndScrollRef } = (0, _usereducer.useUnwrapState)(state);\n    const matchingHead = (0, _react.useMemo)(()=>{\n        return (0, _findheadincache.findHeadInCache)(cache, tree[1]);\n    }, [\n        cache,\n        tree\n    ]);\n    // Add memoized pathParams for useParams.\n    const pathParams = (0, _react.useMemo)(()=>{\n        return (0, _computechangedpath.getSelectedParams)(tree);\n    }, [\n        tree\n    ]);\n    const layoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            parentTree: tree,\n            parentCacheNode: cache,\n            parentSegmentPath: null,\n            // Root node always has `url`\n            // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n            url: canonicalUrl\n        };\n    }, [\n        tree,\n        cache,\n        canonicalUrl\n    ]);\n    const globalLayoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            changeByServerResponse,\n            tree,\n            focusAndScrollRef,\n            nextUrl\n        };\n    }, [\n        changeByServerResponse,\n        tree,\n        focusAndScrollRef,\n        nextUrl\n    ]);\n    let head;\n    if (matchingHead !== null) {\n        // The head is wrapped in an extra component so we can use\n        // `useDeferredValue` to swap between the prefetched and final versions of\n        // the head. (This is what LayoutRouter does for segment data, too.)\n        //\n        // The `key` is used to remount the component whenever the head moves to\n        // a different segment.\n        const [headCacheNode, headKey] = matchingHead;\n        head = /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {\n            headCacheNode: headCacheNode\n        }, headKey);\n    } else {\n        head = null;\n    }\n    let content = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_redirectboundary.RedirectBoundary, {\n        children: [\n            head,\n            cache.rsc,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuterannouncer.AppRouterAnnouncer, {\n                tree: tree\n            })\n        ]\n    });\n    if (true) {\n        // In development, we apply few error boundaries and hot-reloader:\n        // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n        // - HotReloader:\n        //  - hot-reload the app when the code changes\n        //  - render dev overlay\n        //  - catch runtime errors and display global-error when necessary\n        if (true) {\n            const { DevRootHTTPAccessFallbackBoundary } = __webpack_require__(/*! ./dev-root-http-access-fallback-boundary */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js\");\n            content = /*#__PURE__*/ (0, _jsxruntime.jsx)(DevRootHTTPAccessFallbackBoundary, {\n                children: content\n            });\n        }\n        const HotReloader = (__webpack_require__(/*! ./react-dev-overlay/app/hot-reloader-client */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\")[\"default\"]);\n        content = /*#__PURE__*/ (0, _jsxruntime.jsx)(HotReloader, {\n            assetPrefix: assetPrefix,\n            globalError: globalError,\n            children: content\n        });\n    } else {}\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(HistoryUpdater, {\n                appRouterState: (0, _usereducer.useUnwrapState)(state)\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(RuntimeStyles, {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathParamsContext.Provider, {\n                value: pathParams,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathnameContext.Provider, {\n                    value: pathname,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.SearchParamsContext.Provider, {\n                        value: searchParams,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.GlobalLayoutRouterContext.Provider, {\n                            value: globalLayoutRouterContext,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.AppRouterContext.Provider, {\n                                value: appRouter,\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n                                    value: layoutRouterContext,\n                                    children: content\n                                })\n                            })\n                        })\n                    })\n                })\n            })\n        ]\n    });\n}\n_s(Router, \"bU8t8nCPb2ycaFr1siwKA2Gych0=\", false, function() {\n    return [\n        useChangeByServerResponse,\n        useNavigate\n    ];\n});\n_c2 = Router;\nfunction AppRouter(param) {\n    let { actionQueue, globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles], assetPrefix } = param;\n    (0, _navfailurehandler.useNavFailureHandler)();\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.ErrorBoundary, {\n        // At the very top level, use the default GlobalError component as the final fallback.\n        // When the app router itself fails, which means the framework itself fails, we show the default error.\n        errorComponent: _errorboundary.default,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Router, {\n            actionQueue: actionQueue,\n            assetPrefix: assetPrefix,\n            globalError: [\n                globalErrorComponent,\n                globalErrorStyles\n            ]\n        })\n    });\n}\n_c3 = AppRouter;\nconst runtimeStyles = new Set();\nlet runtimeStyleChanged = new Set();\nglobalThis._N_E_STYLE_LOAD = function(href) {\n    let len = runtimeStyles.size;\n    runtimeStyles.add(href);\n    if (runtimeStyles.size !== len) {\n        runtimeStyleChanged.forEach((cb)=>cb());\n    }\n    // TODO figure out how to get a promise here\n    // But maybe it's not necessary as react would block rendering until it's loaded\n    return Promise.resolve();\n};\nfunction RuntimeStyles() {\n    _s1();\n    const [, forceUpdate] = _react.default.useState(0);\n    const renderedStylesSize = runtimeStyles.size;\n    (0, _react.useEffect)(()=>{\n        const changed = ()=>forceUpdate((c)=>c + 1);\n        runtimeStyleChanged.add(changed);\n        if (renderedStylesSize !== runtimeStyles.size) {\n            changed();\n        }\n        return ()=>{\n            runtimeStyleChanged.delete(changed);\n        };\n    }, [\n        renderedStylesSize,\n        forceUpdate\n    ]);\n    const dplId =  false ? 0 : '';\n    return [\n        ...runtimeStyles\n    ].map((href, i)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n            rel: \"stylesheet\",\n            href: \"\" + href + dplId,\n            // @ts-ignore\n            precedence: \"next\"\n        }, i));\n}\n_s1(RuntimeStyles, \"Eht7Kgdrrgt5B4LSklQ7qDPo8Aw=\");\n_c4 = RuntimeStyles;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router.js.map\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HistoryUpdater\");\n$RefreshReg$(_c1, \"Head\");\n$RefreshReg$(_c2, \"Router\");\n$RefreshReg$(_c3, \"AppRouter\");\n$RefreshReg$(_c4, \"RuntimeStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/app-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"HTTPAccessFallbackBoundary\", ({\n    enumerable: true,\n    get: function() {\n        return HTTPAccessFallbackBoundary;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\"));\nconst _navigationuntracked = __webpack_require__(/*! ../navigation-untracked */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/navigation-untracked.js\");\nconst _httpaccessfallback = __webpack_require__(/*! ./http-access-fallback */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js\");\nconst _warnonce = __webpack_require__(/*! ../../../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nclass HTTPAccessFallbackErrorBoundary extends _react.default.Component {\n    componentDidCatch() {\n        if ( true && this.props.missingSlots && this.props.missingSlots.size > 0 && // A missing children slot is the typical not-found case, so no need to warn\n        !this.props.missingSlots.has('children')) {\n            let warningMessage = 'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n';\n            const formattedSlots = Array.from(this.props.missingSlots).sort((a, b)=>a.localeCompare(b)).map((slot)=>\"@\" + slot).join(', ');\n            warningMessage += 'Missing slots: ' + formattedSlots;\n            (0, _warnonce.warnOnce)(warningMessage);\n        }\n    }\n    static getDerivedStateFromError(error) {\n        if ((0, _httpaccessfallback.isHTTPAccessFallbackError)(error)) {\n            const httpStatus = (0, _httpaccessfallback.getAccessFallbackHTTPStatus)(error);\n            return {\n                triggeredStatus: httpStatus\n            };\n        }\n        // Re-throw if error is not for 404\n        throw error;\n    }\n    static getDerivedStateFromProps(props, state) {\n        /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */ if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n            return {\n                triggeredStatus: undefined,\n                previousPathname: props.pathname\n            };\n        }\n        return {\n            triggeredStatus: state.triggeredStatus,\n            previousPathname: props.pathname\n        };\n    }\n    render() {\n        const { notFound, forbidden, unauthorized, children } = this.props;\n        const { triggeredStatus } = this.state;\n        const errorComponents = {\n            [_httpaccessfallback.HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n            [_httpaccessfallback.HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n            [_httpaccessfallback.HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized\n        };\n        if (triggeredStatus) {\n            const isNotFound = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.NOT_FOUND && notFound;\n            const isForbidden = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.FORBIDDEN && forbidden;\n            const isUnauthorized = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized;\n            // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n            if (!(isNotFound || isForbidden || isUnauthorized)) {\n                return children;\n            }\n            return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                        name: \"robots\",\n                        content: \"noindex\"\n                    }),\n                     true && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                        name: \"boundary-next-error\",\n                        content: (0, _httpaccessfallback.getAccessFallbackErrorTypeByStatus)(triggeredStatus)\n                    }),\n                    errorComponents[triggeredStatus]\n                ]\n            });\n        }\n        return children;\n    }\n    constructor(props){\n        super(props);\n        this.state = {\n            triggeredStatus: undefined,\n            previousPathname: props.pathname\n        };\n    }\n}\nfunction HTTPAccessFallbackBoundary(param) {\n    let { notFound, forbidden, unauthorized, children } = param;\n    // When we're rendering the missing params shell, this will return null. This\n    // is because we won't be rendering any not found boundaries or error\n    // boundaries for the missing params shell. When this runs on the client\n    // (where these error can occur), we will get the correct pathname.\n    const pathname = (0, _navigationuntracked.useUntrackedPathname)();\n    const missingSlots = (0, _react.useContext)(_approutercontextsharedruntime.MissingSlotContext);\n    const hasErrorFallback = !!(notFound || forbidden || unauthorized);\n    if (hasErrorFallback) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(HTTPAccessFallbackErrorBoundary, {\n            pathname: pathname,\n            notFound: notFound,\n            forbidden: forbidden,\n            unauthorized: unauthorized,\n            missingSlots: missingSlots,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c = HTTPAccessFallbackBoundary;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-boundary.js.map\nvar _c;\n$RefreshReg$(_c, \"HTTPAccessFallbackBoundary\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/links.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/links.js ***!
  \**************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    mountLinkInstance: function() {\n        return mountLinkInstance;\n    },\n    onLinkVisibilityChanged: function() {\n        return onLinkVisibilityChanged;\n    },\n    onNavigationIntent: function() {\n        return onNavigationIntent;\n    },\n    pingVisibleLinks: function() {\n        return pingVisibleLinks;\n    },\n    unmountLinkInstance: function() {\n        return unmountLinkInstance;\n    }\n});\nconst _actionqueue = __webpack_require__(/*! ../../shared/lib/router/action-queue */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/router/action-queue.js\");\nconst _approuter = __webpack_require__(/*! ./app-router */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/app-router.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/segment-cache.js\");\n// Use a WeakMap to associate a Link instance with its DOM element. This is\n// used by the IntersectionObserver to track the link's visibility.\nconst links = typeof WeakMap === 'function' ? new WeakMap() : new Map();\n// A Set of the currently visible links. We re-prefetch visible links after a\n// cache invalidation, or when the current URL changes. It's a separate data\n// structure from the WeakMap above because only the visible links need to\n// be enumerated.\nconst visibleLinks = new Set();\n// A single IntersectionObserver instance shared by all <Link> components.\nconst observer = typeof IntersectionObserver === 'function' ? new IntersectionObserver(handleIntersect, {\n    rootMargin: '200px'\n}) : null;\nfunction mountLinkInstance(element, href, router, kind) {\n    let prefetchUrl = null;\n    try {\n        prefetchUrl = (0, _approuter.createPrefetchURL)(href);\n        if (prefetchUrl === null) {\n            // We only track the link if it's prefetchable. For example, this excludes\n            // links to external URLs.\n            return;\n        }\n    } catch (e) {\n        // createPrefetchURL sometimes throws an error if an invalid URL is\n        // provided, though I'm not sure if it's actually necessary.\n        // TODO: Consider removing the throw from the inner function, or change it\n        // to reportError. Or maybe the error isn't even necessary for automatic\n        // prefetches, just navigations.\n        const reportErrorFn = typeof reportError === 'function' ? reportError : console.error;\n        reportErrorFn(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\");\n        return;\n    }\n    const instance = {\n        prefetchHref: prefetchUrl.href,\n        router,\n        kind,\n        isVisible: false,\n        wasHoveredOrTouched: false,\n        prefetchTask: null,\n        cacheVersion: -1\n    };\n    const existingInstance = links.get(element);\n    if (existingInstance !== undefined) {\n        // This shouldn't happen because each <Link> component should have its own\n        // anchor tag instance, but it's defensive coding to avoid a memory leak in\n        // case there's a logical error somewhere else.\n        unmountLinkInstance(element);\n    }\n    links.set(element, instance);\n    if (observer !== null) {\n        observer.observe(element);\n    }\n}\nfunction unmountLinkInstance(element) {\n    const instance = links.get(element);\n    if (instance !== undefined) {\n        links.delete(element);\n        visibleLinks.delete(instance);\n        const prefetchTask = instance.prefetchTask;\n        if (prefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(prefetchTask);\n        }\n    }\n    if (observer !== null) {\n        observer.unobserve(element);\n    }\n}\nfunction handleIntersect(entries) {\n    for (const entry of entries){\n        // Some extremely old browsers or polyfills don't reliably support\n        // isIntersecting so we check intersectionRatio instead. (Do we care? Not\n        // really. But whatever this is fine.)\n        const isVisible = entry.intersectionRatio > 0;\n        onLinkVisibilityChanged(entry.target, isVisible);\n    }\n}\nfunction onLinkVisibilityChanged(element, isVisible) {\n    if (true) {\n        // Prefetching on viewport is disabled in development for performance\n        // reasons, because it requires compiling the target page.\n        // TODO: Investigate re-enabling this.\n        return;\n    }\n    const instance = links.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    instance.isVisible = isVisible;\n    if (isVisible) {\n        visibleLinks.add(instance);\n    } else {\n        visibleLinks.delete(instance);\n    }\n    rescheduleLinkPrefetch(instance);\n}\nfunction onNavigationIntent(element) {\n    const instance = links.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    // Prefetch the link on hover/touchstart.\n    if (instance !== undefined) {\n        instance.wasHoveredOrTouched = true;\n        rescheduleLinkPrefetch(instance);\n    }\n}\nfunction rescheduleLinkPrefetch(instance) {\n    const existingPrefetchTask = instance.prefetchTask;\n    if (!instance.isVisible) {\n        // Cancel any in-progress prefetch task. (If it already finished then this\n        // is a no-op.)\n        if (existingPrefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(existingPrefetchTask);\n        }\n        // We don't need to reset the prefetchTask to null upon cancellation; an\n        // old task object can be rescheduled with bumpPrefetchTask. This is a\n        // micro-optimization but also makes the code simpler (don't need to\n        // worry about whether an old task object is stale).\n        return;\n    }\n    if (true) {\n        // The old prefetch implementation does not have different priority levels.\n        // Just schedule a new prefetch task.\n        prefetchWithOldCacheImplementation(instance);\n        return;\n    }\n    // In the Segment Cache implementation, we assign a higher priority level to\n    // links that were at one point hovered or touched. Since the queue is last-\n    // in-first-out, the highest priority Link is whichever one was hovered last.\n    //\n    // We also increase the relative priority of links whenever they re-enter the\n    // viewport, as if they were being scheduled for the first time.\n    const priority = instance.wasHoveredOrTouched ? _segmentcache.PrefetchPriority.Intent : _segmentcache.PrefetchPriority.Default;\n    if (existingPrefetchTask === null) {\n        // Initiate a prefetch task.\n        const appRouterState = (0, _actionqueue.getCurrentAppRouterState)();\n        if (appRouterState !== null) {\n            const nextUrl = appRouterState.nextUrl;\n            const treeAtTimeOfPrefetch = appRouterState.tree;\n            const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n            instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, treeAtTimeOfPrefetch, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n            instance.cacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n        }\n    } else {\n        // We already have an old task object that we can reschedule. This is\n        // effectively the same as canceling the old task and creating a new one.\n        (0, _segmentcache.bumpPrefetchTask)(existingPrefetchTask, priority);\n    }\n}\nfunction pingVisibleLinks(nextUrl, tree) {\n    // For each currently visible link, cancel the existing prefetch task (if it\n    // exists) and schedule a new one. This is effectively the same as if all the\n    // visible links left and then re-entered the viewport.\n    //\n    // This is called when the Next-Url or the base tree changes, since those\n    // may affect the result of a prefetch task. It's also called after a\n    // cache invalidation.\n    const currentCacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    for (const instance of visibleLinks){\n        const task = instance.prefetchTask;\n        if (task !== null && instance.cacheVersion === currentCacheVersion && task.key.nextUrl === nextUrl && task.treeAtTimeOfPrefetch === tree) {\n            continue;\n        }\n        // Something changed. Cancel the existing prefetch task and schedule a\n        // new one.\n        if (task !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(task);\n        }\n        const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n        const priority = instance.wasHoveredOrTouched ? _segmentcache.PrefetchPriority.Intent : _segmentcache.PrefetchPriority.Default;\n        instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, tree, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n        instance.cacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    }\n}\nfunction prefetchWithOldCacheImplementation(instance) {\n    // This is the path used when the Segment Cache is not enabled.\n    if (false) {}\n    const doPrefetch = async ()=>{\n        // note that `appRouter.prefetch()` is currently sync,\n        // so we have to wrap this call in an async function to be able to catch() errors below.\n        return instance.router.prefetch(instance.prefetchHref, {\n            kind: instance.kind\n        });\n    };\n    // Prefetch the page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    doPrefetch().catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=links.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/links.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createInitialRouterState\", ({\n    enumerable: true,\n    get: function() {\n        return createInitialRouterState;\n    }\n}));\nconst _createhreffromurl = __webpack_require__(/*! ./create-href-from-url */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _filllazyitemstillleafwithhead = __webpack_require__(/*! ./fill-lazy-items-till-leaf-with-head */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.js\");\nconst _computechangedpath = __webpack_require__(/*! ./compute-changed-path */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _prefetchcacheutils = __webpack_require__(/*! ./prefetch-cache-utils */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _refetchinactiveparallelsegments = __webpack_require__(/*! ./refetch-inactive-parallel-segments */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js\");\nconst _flightdatahelpers = __webpack_require__(/*! ../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/flight-data-helpers.js\");\nfunction createInitialRouterState(param) {\n    let { initialFlightData, initialCanonicalUrlParts, initialParallelRoutes, location, couldBeIntercepted, postponed, prerendered } = param;\n    // When initialized on the server, the canonical URL is provided as an array of parts.\n    // This is to ensure that when the RSC payload streamed to the client, crawlers don't interpret it\n    // as a URL that should be crawled.\n    const initialCanonicalUrl = initialCanonicalUrlParts.join('/');\n    const normalizedFlightData = (0, _flightdatahelpers.getFlightDataPartsFromPath)(initialFlightData[0]);\n    const { tree: initialTree, seedData: initialSeedData, head: initialHead } = normalizedFlightData;\n    // For the SSR render, seed data should always be available (we only send back a `null` response\n    // in the case of a `loading` segment, pre-PPR.)\n    const rsc = initialSeedData == null ? void 0 : initialSeedData[1];\n    var _initialSeedData_;\n    const loading = (_initialSeedData_ = initialSeedData == null ? void 0 : initialSeedData[3]) != null ? _initialSeedData_ : null;\n    const cache = {\n        lazyData: null,\n        rsc,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        // The cache gets seeded during the first render. `initialParallelRoutes` ensures the cache from the first render is there during the second render.\n        parallelRoutes: initialParallelRoutes,\n        loading\n    };\n    const canonicalUrl = // This is safe to do as canonicalUrl can't be rendered, it's only used to control the history updates in the useEffect further down in this file.\n    location ? (0, _createhreffromurl.createHrefFromUrl)(location) : initialCanonicalUrl;\n    (0, _refetchinactiveparallelsegments.addRefreshMarkerToActiveParallelSegments)(initialTree, canonicalUrl);\n    const prefetchCache = new Map();\n    // When the cache hasn't been seeded yet we fill the cache with the head.\n    if (initialParallelRoutes === null || initialParallelRoutes.size === 0) {\n        (0, _filllazyitemstillleafwithhead.fillLazyItemsTillLeafWithHead)(cache, undefined, initialTree, initialSeedData, initialHead, undefined);\n    }\n    var _ref;\n    const initialState = {\n        tree: initialTree,\n        cache,\n        prefetchCache,\n        pushRef: {\n            pendingPush: false,\n            mpaNavigation: false,\n            // First render needs to preserve the previous window.history.state\n            // to avoid it being overwritten on navigation back/forward with MPA Navigation.\n            preserveCustomHistoryState: true\n        },\n        focusAndScrollRef: {\n            apply: false,\n            onlyHashChange: false,\n            hashFragment: null,\n            segmentPaths: []\n        },\n        canonicalUrl,\n        nextUrl: (_ref = (0, _computechangedpath.extractPathFromFlightRouterState)(initialTree) || (location == null ? void 0 : location.pathname)) != null ? _ref : null\n    };\n    if (false) {}\n    return initialState;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=create-initial-router-state.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    STATIC_STALETIME_MS: function() {\n        return STATIC_STALETIME_MS;\n    },\n    createSeededPrefetchCacheEntry: function() {\n        return createSeededPrefetchCacheEntry;\n    },\n    getOrCreatePrefetchCacheEntry: function() {\n        return getOrCreatePrefetchCacheEntry;\n    },\n    prunePrefetchCache: function() {\n        return prunePrefetchCache;\n    }\n});\nconst _fetchserverresponse = __webpack_require__(/*! ./fetch-server-response */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst INTERCEPTION_CACHE_KEY_MARKER = '%';\n/**\n * Creates a cache key for the router prefetch cache\n *\n * @param url - The URL being navigated to\n * @param nextUrl - an internal URL, primarily used for handling rewrites. Defaults to '/'.\n * @return The generated prefetch cache key.\n */ function createPrefetchCacheKeyImpl(url, includeSearchParams, prefix) {\n    // Initially we only use the pathname as the cache key. We don't want to include\n    // search params so that multiple URLs with the same search parameter can re-use\n    // loading states.\n    let pathnameFromUrl = url.pathname;\n    // RSC responses can differ based on search params, specifically in the case where we aren't\n    // returning a partial response (ie with `PrefetchKind.AUTO`).\n    // In the auto case, since loading.js & layout.js won't have access to search params,\n    // we can safely re-use that cache entry. But for full prefetches, we should not\n    // re-use the cache entry as the response may differ.\n    if (includeSearchParams) {\n        // if we have a full prefetch, we can include the search param in the key,\n        // as we'll be getting back a full response. The server might have read the search\n        // params when generating the full response.\n        pathnameFromUrl += url.search;\n    }\n    if (prefix) {\n        return \"\" + prefix + INTERCEPTION_CACHE_KEY_MARKER + pathnameFromUrl;\n    }\n    return pathnameFromUrl;\n}\nfunction createPrefetchCacheKey(url, kind, nextUrl) {\n    return createPrefetchCacheKeyImpl(url, kind === _routerreducertypes.PrefetchKind.FULL, nextUrl);\n}\nfunction getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing) {\n    if (kind === void 0) kind = _routerreducertypes.PrefetchKind.TEMPORARY;\n    // We first check if there's a more specific interception route prefetch entry\n    // This is because when we detect a prefetch that corresponds with an interception route, we prefix it with nextUrl (see `createPrefetchCacheKey`)\n    // to avoid conflicts with other pages that may have the same URL but render different things depending on the `Next-URL` header.\n    for (const maybeNextUrl of [\n        nextUrl,\n        null\n    ]){\n        const cacheKeyWithParams = createPrefetchCacheKeyImpl(url, true, maybeNextUrl);\n        const cacheKeyWithoutParams = createPrefetchCacheKeyImpl(url, false, maybeNextUrl);\n        // First, we check if we have a cache entry that exactly matches the URL\n        const cacheKeyToUse = url.search ? cacheKeyWithParams : cacheKeyWithoutParams;\n        const existingEntry = prefetchCache.get(cacheKeyToUse);\n        if (existingEntry && allowAliasing) {\n            // We know we're returning an aliased entry when the pathname matches but the search params don't,\n            const isAliased = existingEntry.url.pathname === url.pathname && existingEntry.url.search !== url.search;\n            if (isAliased) {\n                return {\n                    ...existingEntry,\n                    aliased: true\n                };\n            }\n            return existingEntry;\n        }\n        // If the request contains search params, and we're not doing a full prefetch, we can return the\n        // param-less entry if it exists.\n        // This is technically covered by the check at the bottom of this function, which iterates over cache entries,\n        // but lets us arrive there quicker in the param-full case.\n        const entryWithoutParams = prefetchCache.get(cacheKeyWithoutParams);\n        if (false) {}\n    }\n    // If we've gotten to this point, we didn't find a specific cache entry that matched\n    // the request URL.\n    // We attempt a partial match by checking if there's a cache entry with the same pathname.\n    // Regardless of what we find, since it doesn't correspond with the requested URL, we'll mark it \"aliased\".\n    // This will signal to the router that it should only apply the loading state on the prefetched data.\n    if (false) {}\n    return undefined;\n}\nfunction getOrCreatePrefetchCacheEntry(param) {\n    let { url, nextUrl, tree, prefetchCache, kind, allowAliasing = true } = param;\n    const existingCacheEntry = getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing);\n    if (existingCacheEntry) {\n        // Grab the latest status of the cache entry and update it\n        existingCacheEntry.status = getPrefetchEntryCacheStatus(existingCacheEntry);\n        // when `kind` is provided, an explicit prefetch was requested.\n        // if the requested prefetch is \"full\" and the current cache entry wasn't, we want to re-prefetch with the new intent\n        const switchedToFullPrefetch = existingCacheEntry.kind !== _routerreducertypes.PrefetchKind.FULL && kind === _routerreducertypes.PrefetchKind.FULL;\n        if (switchedToFullPrefetch) {\n            // If we switched to a full prefetch, validate that the existing cache entry contained partial data.\n            // It's possible that the cache entry was seeded with full data but has a cache type of \"auto\" (ie when cache entries\n            // are seeded but without a prefetch intent)\n            existingCacheEntry.data.then((prefetchResponse)=>{\n                const isFullPrefetch = Array.isArray(prefetchResponse.flightData) && prefetchResponse.flightData.some((flightData)=>{\n                    // If we started rendering from the root and we returned RSC data (seedData), we already had a full prefetch.\n                    return flightData.isRootRender && flightData.seedData !== null;\n                });\n                if (!isFullPrefetch) {\n                    return createLazyPrefetchEntry({\n                        tree,\n                        url,\n                        nextUrl,\n                        prefetchCache,\n                        // If we didn't get an explicit prefetch kind, we want to set a temporary kind\n                        // rather than assuming the same intent as the previous entry, to be consistent with how we\n                        // lazily create prefetch entries when intent is left unspecified.\n                        kind: kind != null ? kind : _routerreducertypes.PrefetchKind.TEMPORARY\n                    });\n                }\n            });\n        }\n        // If the existing cache entry was marked as temporary, it means it was lazily created when attempting to get an entry,\n        // where we didn't have the prefetch intent. Now that we have the intent (in `kind`), we want to update the entry to the more accurate kind.\n        if (kind && existingCacheEntry.kind === _routerreducertypes.PrefetchKind.TEMPORARY) {\n            existingCacheEntry.kind = kind;\n        }\n        // We've determined that the existing entry we found is still valid, so we return it.\n        return existingCacheEntry;\n    }\n    // If we didn't return an entry, create a new one.\n    return createLazyPrefetchEntry({\n        tree,\n        url,\n        nextUrl,\n        prefetchCache,\n        kind: kind || _routerreducertypes.PrefetchKind.TEMPORARY\n    });\n}\n/*\n * Used to take an existing cache entry and prefix it with the nextUrl, if it exists.\n * This ensures that we don't have conflicting cache entries for the same URL (as is the case with route interception).\n */ function prefixExistingPrefetchCacheEntry(param) {\n    let { url, nextUrl, prefetchCache, existingCacheKey } = param;\n    const existingCacheEntry = prefetchCache.get(existingCacheKey);\n    if (!existingCacheEntry) {\n        // no-op -- there wasn't an entry to move\n        return;\n    }\n    const newCacheKey = createPrefetchCacheKey(url, existingCacheEntry.kind, nextUrl);\n    prefetchCache.set(newCacheKey, {\n        ...existingCacheEntry,\n        key: newCacheKey\n    });\n    prefetchCache.delete(existingCacheKey);\n    return newCacheKey;\n}\nfunction createSeededPrefetchCacheEntry(param) {\n    let { nextUrl, tree, prefetchCache, url, data, kind } = param;\n    // The initial cache entry technically includes full data, but it isn't explicitly prefetched -- we just seed the\n    // prefetch cache so that we can skip an extra prefetch request later, since we already have the data.\n    // if the prefetch corresponds with an interception route, we use the nextUrl to prefix the cache key\n    const prefetchCacheKey = data.couldBeIntercepted ? createPrefetchCacheKey(url, kind, nextUrl) : createPrefetchCacheKey(url, kind);\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data: Promise.resolve(data),\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: Date.now(),\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\n/**\n * Creates a prefetch entry entry and enqueues a fetch request to retrieve the data.\n */ function createLazyPrefetchEntry(param) {\n    let { url, kind, tree, nextUrl, prefetchCache } = param;\n    const prefetchCacheKey = createPrefetchCacheKey(url, kind);\n    // initiates the fetch request for the prefetch and attaches a listener\n    // to the promise to update the prefetch cache entry when the promise resolves (if necessary)\n    const data = _prefetchreducer.prefetchQueue.enqueue(()=>(0, _fetchserverresponse.fetchServerResponse)(url, {\n            flightRouterState: tree,\n            nextUrl,\n            prefetchKind: kind\n        }).then((prefetchResponse)=>{\n            // TODO: `fetchServerResponse` should be more tighly coupled to these prefetch cache operations\n            // to avoid drift between this cache key prefixing logic\n            // (which is currently directly influenced by the server response)\n            let newCacheKey;\n            if (prefetchResponse.couldBeIntercepted) {\n                // Determine if we need to prefix the cache key with the nextUrl\n                newCacheKey = prefixExistingPrefetchCacheEntry({\n                    url,\n                    existingCacheKey: prefetchCacheKey,\n                    nextUrl,\n                    prefetchCache\n                });\n            }\n            // If the prefetch was a cache hit, we want to update the existing cache entry to reflect that it was a full prefetch.\n            // This is because we know that a static response will contain the full RSC payload, and can be updated to respect the `static`\n            // staleTime.\n            if (prefetchResponse.prerendered) {\n                const existingCacheEntry = prefetchCache.get(newCacheKey != null ? newCacheKey : prefetchCacheKey);\n                if (existingCacheEntry) {\n                    existingCacheEntry.kind = _routerreducertypes.PrefetchKind.FULL;\n                    if (prefetchResponse.staleTime !== -1) {\n                        // This is the stale time that was collected by the server during\n                        // static generation. Use this in place of the default stale time.\n                        existingCacheEntry.staleTime = prefetchResponse.staleTime;\n                    }\n                }\n            }\n            return prefetchResponse;\n        }));\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data,\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: null,\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\nfunction prunePrefetchCache(prefetchCache) {\n    for (const [href, prefetchCacheEntry] of prefetchCache){\n        if (getPrefetchEntryCacheStatus(prefetchCacheEntry) === _routerreducertypes.PrefetchCacheEntryStatus.expired) {\n            prefetchCache.delete(href);\n        }\n    }\n}\n// These values are set by `define-env-plugin` (based on `nextConfig.experimental.staleTimes`)\n// and default to 5 minutes (static) / 0 seconds (dynamic)\nconst DYNAMIC_STALETIME_MS = Number(\"0\") * 1000;\nconst STATIC_STALETIME_MS = Number(\"300\") * 1000;\nfunction getPrefetchEntryCacheStatus(param) {\n    let { kind, prefetchTime, lastUsedTime, staleTime } = param;\n    if (staleTime !== -1) {\n        // `staleTime` is the value sent by the server during static generation.\n        // When this is available, it takes precedence over any of the heuristics\n        // that follow.\n        //\n        // TODO: When PPR is enabled, the server will *always* return a stale time\n        // when prefetching. We should never use a prefetch entry that hasn't yet\n        // received data from the server. So the only two cases should be 1) we use\n        // the server-generated stale time 2) the unresolved entry is discarded.\n        return Date.now() < prefetchTime + staleTime ? _routerreducertypes.PrefetchCacheEntryStatus.fresh : _routerreducertypes.PrefetchCacheEntryStatus.stale;\n    }\n    // We will re-use the cache entry data for up to the `dynamic` staletime window.\n    if (Date.now() < (lastUsedTime != null ? lastUsedTime : prefetchTime) + DYNAMIC_STALETIME_MS) {\n        return lastUsedTime ? _routerreducertypes.PrefetchCacheEntryStatus.reusable : _routerreducertypes.PrefetchCacheEntryStatus.fresh;\n    }\n    // For \"auto\" prefetching, we'll re-use only the loading boundary for up to `static` staletime window.\n    // A stale entry will only re-use the `loading` boundary, not the full data.\n    // This will trigger a \"lazy fetch\" for the full data.\n    if (kind === _routerreducertypes.PrefetchKind.AUTO) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.stale;\n        }\n    }\n    // for \"full\" prefetching, we'll re-use the cache entry data for up to `static` staletime window.\n    if (kind === _routerreducertypes.PrefetchKind.FULL) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.reusable;\n        }\n    }\n    return _routerreducertypes.PrefetchCacheEntryStatus.expired;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=prefetch-cache-utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// This file is only used in app router due to the specific error state handling.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    onCaughtError: function() {\n        return onCaughtError;\n    },\n    onUncaughtError: function() {\n        return onUncaughtError;\n    }\n});\nconst _stitchederror = __webpack_require__(/*! ../components/errors/stitched-error */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/errors/stitched-error.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../components/errors/use-error-handler */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _isnextroutererror = __webpack_require__(/*! ../components/is-next-router-error */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _bailouttocsr = __webpack_require__(/*! ../../shared/lib/lazy-dynamic/bailout-to-csr */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nconst _reportglobalerror = __webpack_require__(/*! ./report-global-error */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/react-client-callbacks/report-global-error.js\");\nconst _interceptconsoleerror = __webpack_require__(/*! ../components/globals/intercept-console-error */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/globals/intercept-console-error.js\");\nconst _errorboundary = __webpack_require__(/*! ../components/error-boundary */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js\");\nfunction onCaughtError(err, errorInfo) {\n    var _errorInfo_errorBoundary;\n    const errorBoundaryComponent = (_errorInfo_errorBoundary = errorInfo.errorBoundary) == null ? void 0 : _errorInfo_errorBoundary.constructor;\n    let isImplicitErrorBoundary;\n    if (true) {\n        const { AppDevOverlayErrorBoundary } = __webpack_require__(/*! ../components/react-dev-overlay/app/app-dev-overlay-error-boundary */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js\");\n        isImplicitErrorBoundary = errorBoundaryComponent === AppDevOverlayErrorBoundary;\n    }\n    isImplicitErrorBoundary = isImplicitErrorBoundary || errorBoundaryComponent === _errorboundary.ErrorBoundaryHandler && errorInfo.errorBoundary.props.errorComponent === _errorboundary.GlobalError;\n    if (isImplicitErrorBoundary) {\n        // We don't consider errors caught unless they're caught by an explicit error\n        // boundary. The built-in ones are considered implicit.\n        // This mimics how the same app would behave without Next.js.\n        return onUncaughtError(err, errorInfo);\n    }\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(err) || (0, _isnextroutererror.isNextRouterError)(err)) return;\n    if (true) {\n        var _errorInfo_componentStack;\n        const errorBoundaryName = (errorBoundaryComponent == null ? void 0 : errorBoundaryComponent.displayName) || (errorBoundaryComponent == null ? void 0 : errorBoundaryComponent.name) || 'Unknown';\n        const componentThatErroredFrame = errorInfo == null ? void 0 : (_errorInfo_componentStack = errorInfo.componentStack) == null ? void 0 : _errorInfo_componentStack.split('\\n')[1];\n        var // example 1: at Page (http://localhost:3000/_next/static/chunks/pages/index.js?ts=1631600000000:2:1)\n        // example 2: Page@http://localhost:3000/_next/static/chunks/pages/index.js?ts=1631600000000:2:1\n        _componentThatErroredFrame_match;\n        // Match chrome or safari stack trace\n        const matches = (_componentThatErroredFrame_match = componentThatErroredFrame == null ? void 0 : componentThatErroredFrame.match(/\\s+at (\\w+)\\s+|(\\w+)@/)) != null ? _componentThatErroredFrame_match : [];\n        const componentThatErroredName = matches[1] || matches[2] || 'Unknown';\n        // Create error location with errored component and error boundary, to match the behavior of default React onCaughtError handler.\n        const errorBoundaryMessage = \"It was handled by the <\" + errorBoundaryName + \"> error boundary.\";\n        const componentErrorMessage = componentThatErroredName ? \"The above error occurred in the <\" + componentThatErroredName + \"> component.\" : \"The above error occurred in one of your components.\";\n        const errorLocation = componentErrorMessage + \" \" + errorBoundaryMessage;\n        const stitchedError = (0, _stitchederror.getReactStitchedError)(err);\n        // TODO: change to passing down errorInfo later\n        // In development mode, pass along the component stack to the error\n        if (errorInfo.componentStack) {\n            ;\n            stitchedError._componentStack = errorInfo.componentStack;\n        }\n        // Log and report the error with location but without modifying the error stack\n        (0, _interceptconsoleerror.originConsoleError)('%o\\n\\n%s', err, errorLocation);\n        (0, _useerrorhandler.handleClientError)(stitchedError, []);\n    } else {}\n}\nfunction onUncaughtError(err, errorInfo) {\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(err) || (0, _isnextroutererror.isNextRouterError)(err)) return;\n    if (true) {\n        const stitchedError = (0, _stitchederror.getReactStitchedError)(err);\n        // TODO: change to passing down errorInfo later\n        // In development mode, pass along the component stack to the error\n        if (errorInfo.componentStack) {\n            ;\n            stitchedError._componentStack = errorInfo.componentStack;\n        }\n        // TODO: Add an adendum to the overlay telling people about custom error boundaries.\n        (0, _reportglobalerror.reportGlobalError)(stitchedError);\n    } else {}\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-boundary-callbacks.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js\n"));

/***/ })

});