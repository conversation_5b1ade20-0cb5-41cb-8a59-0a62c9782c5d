"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-popper@1.2._735ba650859fcd3c1e79e21390d513c8";
exports.ids = ["vendor-chunks/@radix-ui+react-popper@1.2._735ba650859fcd3c1e79e21390d513c8"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._735ba650859fcd3c1e79e21390d513c8/node_modules/@radix-ui/react-popper/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-popper@1.2._735ba650859fcd3c1e79e21390d513c8/node_modules/@radix-ui/react-popper/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALIGN_OPTIONS: () => (/* binding */ ALIGN_OPTIONS),\n/* harmony export */   Anchor: () => (/* binding */ Anchor),\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Popper: () => (/* binding */ Popper),\n/* harmony export */   PopperAnchor: () => (/* binding */ PopperAnchor),\n/* harmony export */   PopperArrow: () => (/* binding */ PopperArrow),\n/* harmony export */   PopperContent: () => (/* binding */ PopperContent),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   SIDE_OPTIONS: () => (/* binding */ SIDE_OPTIONS),\n/* harmony export */   createPopperScope: () => (/* binding */ createPopperScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/.pnpm/@floating-ui+react-dom@2.1._16ae3335f5a7e1e7c0219d7c95ae90b4/node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/.pnpm/@floating-ui+dom@1.7.2/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\");\n/* harmony import */ var _radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-arrow */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-arrow@1.1.1_8bbd87e91ae262ef454f9567efd09b52/node_modules/@radix-ui/react-arrow/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_2a0e526a8f7e7aada080206d385bb572/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_4fe40d510edca7ae4ca9c92afeb1ae6d/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_997b35f2e2aa9d3174fc03a0f79e437b/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_2dc63ba6354ec7ef7d955ba47145829a/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_7d9c308966eafc0f645092b629b133a3/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1._25ea7b66547079fee23d7c838aabe8ed/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ALIGN_OPTIONS,Anchor,Arrow,Content,Popper,PopperAnchor,PopperArrow,PopperContent,Root,SIDE_OPTIONS,createPopperScope auto */ // packages/react/popper/src/Popper.tsx\n\n\n\n\n\n\n\n\n\n\nvar SIDE_OPTIONS = [\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\"\n];\nvar ALIGN_OPTIONS = [\n    \"start\",\n    \"center\",\n    \"end\"\n];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = (props)=>{\n    const { __scopePopper, children } = props;\n    const [anchor, setAnchor] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperProvider, {\n        scope: __scopePopper,\n        anchor,\n        onAnchorChange: setAnchor,\n        children\n    });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"PopperAnchor.useEffect\": ()=>{\n            context.onAnchorChange(virtualRef?.current || ref.current);\n        }\n    }[\"PopperAnchor.useEffect\"]);\n    return virtualRef ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...anchorProps,\n        ref: composedRefs\n    });\n});\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, side = \"bottom\", sideOffset = 0, align = \"center\", alignOffset = 0, arrowPadding = 0, avoidCollisions = true, collisionBoundary = [], collisionPadding: collisionPaddingProp = 0, sticky = \"partial\", hideWhenDetached = false, updatePositionStrategy = \"optimized\", onPlaced, ...contentProps } = props;\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"PopperContent.useComposedRefs[composedRefs]\": (node)=>setContent(node)\n    }[\"PopperContent.useComposedRefs[composedRefs]\"]);\n    const [arrow, setArrow] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const arrowSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__.useSize)(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n    const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n    const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : {\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0,\n        ...collisionPaddingProp\n    };\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [\n        collisionBoundary\n    ];\n    const hasExplicitBoundaries = boundary.length > 0;\n    const detectOverflowOptions = {\n        padding: collisionPadding,\n        boundary: boundary.filter(isNotNull),\n        // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n        altBoundary: hasExplicitBoundaries\n    };\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.useFloating)({\n        // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n        strategy: \"fixed\",\n        placement: desiredPlacement,\n        whileElementsMounted: {\n            \"PopperContent.useFloating\": (...args)=>{\n                const cleanup = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__.autoUpdate)(...args, {\n                    animationFrame: updatePositionStrategy === \"always\"\n                });\n                return cleanup;\n            }\n        }[\"PopperContent.useFloating\"],\n        elements: {\n            reference: context.anchor\n        },\n        middleware: [\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.offset)({\n                mainAxis: sideOffset + arrowHeight,\n                alignmentAxis: alignOffset\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.shift)({\n                mainAxis: true,\n                crossAxis: false,\n                limiter: sticky === \"partial\" ? (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.limitShift)() : void 0,\n                ...detectOverflowOptions\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.flip)({\n                ...detectOverflowOptions\n            }),\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.size)({\n                ...detectOverflowOptions,\n                apply: {\n                    \"PopperContent.useFloating\": ({ elements, rects, availableWidth, availableHeight })=>{\n                        const { width: anchorWidth, height: anchorHeight } = rects.reference;\n                        const contentStyle = elements.floating.style;\n                        contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n                        contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n                        contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n                        contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n                    }\n                }[\"PopperContent.useFloating\"]\n            }),\n            arrow && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.arrow)({\n                element: arrow,\n                padding: arrowPadding\n            }),\n            transformOrigin({\n                arrowWidth,\n                arrowHeight\n            }),\n            hideWhenDetached && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.hide)({\n                strategy: \"referenceHidden\",\n                ...detectOverflowOptions\n            })\n        ]\n    });\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const handlePlaced = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onPlaced);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)({\n        \"PopperContent.useLayoutEffect\": ()=>{\n            if (isPositioned) {\n                handlePlaced?.();\n            }\n        }\n    }[\"PopperContent.useLayoutEffect\"], [\n        isPositioned,\n        handlePlaced\n    ]);\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)({\n        \"PopperContent.useLayoutEffect\": ()=>{\n            if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n        }\n    }[\"PopperContent.useLayoutEffect\"], [\n        content\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n        ref: refs.setFloating,\n        \"data-radix-popper-content-wrapper\": \"\",\n        style: {\n            ...floatingStyles,\n            transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n            // keep off the page when measuring\n            minWidth: \"max-content\",\n            zIndex: contentZIndex,\n            [\"--radix-popper-transform-origin\"]: [\n                middlewareData.transformOrigin?.x,\n                middlewareData.transformOrigin?.y\n            ].join(\" \"),\n            // hide the content if using the hide middleware and should be hidden\n            // set visibility to hidden and disable pointer events so the UI behaves\n            // as if the PopperContent isn't there at all\n            ...middlewareData.hide?.referenceHidden && {\n                visibility: \"hidden\",\n                pointerEvents: \"none\"\n            }\n        },\n        dir: props.dir,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperContentProvider, {\n            scope: __scopePopper,\n            placedSide,\n            onArrowChange: setArrow,\n            arrowX,\n            arrowY,\n            shouldHideArrow: cannotCenterArrow,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n                \"data-side\": placedSide,\n                \"data-align\": placedAlign,\n                ...contentProps,\n                ref: composedRefs,\n                style: {\n                    ...contentProps.style,\n                    // if the PopperContent hasn't been placed yet (not all measurements done)\n                    // we prevent animations so that users's animation don't kick in too early referring wrong sides\n                    animation: !isPositioned ? \"none\" : void 0\n                }\n            })\n        })\n    });\n});\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n    top: \"bottom\",\n    right: \"left\",\n    bottom: \"top\",\n    left: \"right\"\n};\nvar PopperArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function PopperArrow2(props, forwardedRef) {\n    const { __scopePopper, ...arrowProps } = props;\n    const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n    const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n    return(// we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", {\n        ref: contentContext.onArrowChange,\n        style: {\n            position: \"absolute\",\n            left: contentContext.arrowX,\n            top: contentContext.arrowY,\n            [baseSide]: 0,\n            transformOrigin: {\n                top: \"\",\n                right: \"0 0\",\n                bottom: \"center 0\",\n                left: \"100% 0\"\n            }[contentContext.placedSide],\n            transform: {\n                top: \"translateY(100%)\",\n                right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n                bottom: `rotate(180deg)`,\n                left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n            }[contentContext.placedSide],\n            visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n        },\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__.Root, {\n            ...arrowProps,\n            ref: forwardedRef,\n            style: {\n                ...arrowProps.style,\n                // ensures the element can be measured correctly (mostly for if SVG)\n                display: \"block\"\n            }\n        })\n    }));\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n    return value !== null;\n}\nvar transformOrigin = (options)=>({\n        name: \"transformOrigin\",\n        options,\n        fn (data) {\n            const { placement, rects, middlewareData } = data;\n            const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n            const isArrowHidden = cannotCenterArrow;\n            const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n            const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n            const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n            const noArrowAlign = {\n                start: \"0%\",\n                center: \"50%\",\n                end: \"100%\"\n            }[placedAlign];\n            const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n            const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n            let x = \"\";\n            let y = \"\";\n            if (placedSide === \"bottom\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${-arrowHeight}px`;\n            } else if (placedSide === \"top\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${rects.floating.height + arrowHeight}px`;\n            } else if (placedSide === \"right\") {\n                x = `${-arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            } else if (placedSide === \"left\") {\n                x = `${rects.floating.width + arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            }\n            return {\n                data: {\n                    x,\n                    y\n                }\n            };\n        }\n    });\nfunction getSideAndAlignFromPlacement(placement) {\n    const [side, align = \"center\"] = placement.split(\"-\");\n    return [\n        side,\n        align\n    ];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._735ba650859fcd3c1e79e21390d513c8/node_modules/@radix-ui/react-popper/dist/index.mjs\n");

/***/ })

};
;