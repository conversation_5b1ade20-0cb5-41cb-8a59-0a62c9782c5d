"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Heart, Shuffle, Sparkles, TrendingUp } from "lucide-react"
import QuickPickEngine from "@/components/quick-pick-engine"
import MyFragrances from "@/components/my-fragrances"
import Discover from "@/components/discover"
import CommunityFeed from "@/components/community-feed"
import Profile from "@/components/profile"
import Navigation from "@/components/navigation"

export default function MyScentSpaceApp() {
  const [activeTab, setActiveTab] = useState("home")

  const trendingStacks = [
    {
      id: 1,
      title: "Summer Citrus Burst",
      fragrances: ["<PERSON><PERSON>qua di Gio", "Light Blue"],
      creator: "ScentMaster",
      likes: 124,
      tags: ["Summer", "Fresh", "Citrus"],
    },
    {
      id: 2,
      title: "Cozy Winter Evening",
      fragrances: ["Tom Ford Tobacco Vanille", "By the Fireplace"],
      creator: "FragranceLover",
      likes: 89,
      tags: ["Winter", "Warm", "Cozy"],
    },
    {
      id: 3,
      title: "Office Professional",
      fragrances: ["Bleu de Chanel", "Sauvage"],
      creator: "BusinessScent",
      likes: 156,
      tags: ["Professional", "Clean", "Versatile"],
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50">
      <div className="container mx-auto px-4 py-6 max-w-md">
        <header className="text-center mb-6">
          <div className="flex items-center justify-center gap-2 mb-2">
            <Sparkles className="h-8 w-8 text-purple-600" />
            <h1 className="text-2xl font-bold text-gray-900">MyScentSpace</h1>
          </div>
          <p className="text-gray-600 text-sm">Discover, layer, and optimize your scent journey</p>
        </header>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-6">
            <TabsTrigger value="home" className="text-xs">
              Home
            </TabsTrigger>
            <TabsTrigger value="collection" className="text-xs">
              Collection
            </TabsTrigger>
            <TabsTrigger value="community" className="text-xs">
              Community
            </TabsTrigger>
            <TabsTrigger value="profile" className="text-xs">
              Profile
            </TabsTrigger>
          </TabsList>

          <TabsContent value="home" className="space-y-6">
            <QuickPickEngine />

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <TrendingUp className="h-5 w-5 text-orange-500" />
                  Trending Stacks
                </CardTitle>
                <CardDescription>Popular layering combinations this week</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {trendingStacks.map((stack) => (
                  <div key={stack.id} className="border rounded-lg p-4 space-y-2">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="font-semibold text-sm">{stack.title}</h4>
                        <p className="text-xs text-gray-600">by {stack.creator}</p>
                      </div>
                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <Heart className="h-3 w-3" />
                        {stack.likes}
                      </div>
                    </div>
                    <div className="text-xs text-gray-700">{stack.fragrances.join(" + ")}</div>
                    <div className="flex flex-wrap gap-1">
                      {stack.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs px-2 py-0">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Shuffle className="h-5 w-5 text-blue-500" />
                  Daily Discovery
                </CardTitle>
                <CardDescription>Try something new today</CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full bg-transparent" variant="outline">
                  <Shuffle className="h-4 w-4 mr-2" />
                  Surprise Me!
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="collection">
            <MyFragrances />
          </TabsContent>

          <TabsContent value="discover">
            <Discover />
          </TabsContent>

          <TabsContent value="community">
            <CommunityFeed />
          </TabsContent>

          <TabsContent value="profile">
            <Profile />
          </TabsContent>
        </Tabs>

        <Navigation activeTab={activeTab} setActiveTab={setActiveTab} />
      </div>
    </div>
  )
}
